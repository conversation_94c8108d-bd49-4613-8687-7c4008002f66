#!/usr/bin/env python3
"""
Coze Studio 本地演示服务器
用于在8888端口运行演示版本
"""

import http.server
import socketserver
import webbrowser
import os
import json
from threading import Timer
from urllib.parse import urlparse, parse_qs

PORT = 8888

class CozeStudioHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.serve_demo_page()
        elif self.path == '/api/status':
            self.serve_api_status()
        elif self.path == '/api/models':
            self.serve_api_models()
        elif self.path == '/api/agents':
            self.serve_api_agents()
        elif self.path.startswith('/api/'):
            self.serve_api_default()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path == '/api/chat':
            self.handle_chat_api()
        else:
            self.send_error(404)
    
    def serve_demo_page(self):
        """提供演示页面"""
        try:
            with open('coze-studio-demo.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "演示页面未找到")
    
    def serve_api_status(self):
        """提供系统状态API"""
        status = {
            "status": "running",
            "version": "demo-1.0.0",
            "services": {
                "docker": "running",
                "port_8888": "available",
                "models": "configured",
                "database": "simulated"
            },
            "deployment": {
                "project_cloned": True,
                "model_configured": True,
                "network_issue": True,
                "demo_running": True
            }
        }
        self.send_json_response(status)
    
    def serve_api_models(self):
        """提供模型列表API"""
        models = {
            "models": [
                {
                    "id": 1,
                    "name": "Doubao-Seed-1.6",
                    "provider": "volcengine",
                    "type": "multimodal",
                    "status": "configured",
                    "capabilities": ["text", "image", "thinking"],
                    "config_file": "backend/conf/model/ark_doubao-seed-1.6.yaml"
                }
            ]
        }
        self.send_json_response(models)
    
    def serve_api_agents(self):
        """提供智能体列表API"""
        agents = {
            "agents": [
                {
                    "id": "agent_001",
                    "name": "智能客服助手",
                    "description": "基于Doubao-Seed-1.6的智能客服系统",
                    "status": "demo",
                    "model": "Doubao-Seed-1.6",
                    "capabilities": ["intent_recognition", "knowledge_search", "response_generation"],
                    "created_at": "2025-08-04T09:00:00Z"
                }
            ]
        }
        self.send_json_response(agents)
    
    def serve_api_default(self):
        """默认API响应"""
        response = {
            "message": "Coze Studio Demo API",
            "note": "这是演示版本，完整功能需要部署真实服务",
            "available_endpoints": [
                "/api/status",
                "/api/models", 
                "/api/agents",
                "/api/chat"
            ]
        }
        self.send_json_response(response)
    
    def handle_chat_api(self):
        """处理聊天API请求"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            # 模拟智能回复
            user_message = data.get('message', '')
            response = {
                "response": f"这是演示回复：我理解您说的是'{user_message}'。在完整版本中，这里会调用真实的AI模型进行回复。",
                "intent": "demo_intent",
                "confidence": 0.95,
                "model": "Doubao-Seed-1.6",
                "timestamp": "2025-08-04T09:00:00Z"
            }
            
            self.send_json_response(response)
        except Exception as e:
            self.send_json_response({"error": str(e)}, status=400)
    
    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def open_browser():
    """延迟打开浏览器"""
    webbrowser.open(f'http://localhost:{PORT}')

def main():
    print("🚀 启动 Coze Studio 演示服务器")
    print("=" * 50)
    
    # 检查演示文件是否存在
    if not os.path.exists('coze-studio-demo.html'):
        print("❌ 错误: coze-studio-demo.html 文件未找到")
        print("请确保在正确的目录中运行此脚本")
        return
    
    try:
        with socketserver.TCPServer(("", PORT), CozeStudioHandler) as httpd:
            print(f"✅ 服务器启动成功")
            print(f"📍 地址: http://localhost:{PORT}")
            print(f"🎯 这是 Coze Studio 的演示版本")
            print(f"📋 展示主要功能和界面设计")
            print(f"🔧 完整功能需要解决Docker网络问题")
            print()
            print("📚 可用的API端点:")
            print("   GET  /api/status  - 系统状态")
            print("   GET  /api/models  - 模型列表")
            print("   GET  /api/agents  - 智能体列表")
            print("   POST /api/chat    - 聊天接口")
            print()
            print("🌐 正在自动打开浏览器...")
            print("按 Ctrl+C 停止服务器")
            
            # 2秒后自动打开浏览器
            Timer(2.0, open_browser).start()
            
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:  # Windows端口被占用错误
            print(f"❌ 错误: 端口 {PORT} 已被占用")
            print("请检查是否有其他服务在使用此端口")
            print("或者修改 PORT 变量使用其他端口")
        else:
            print(f"❌ 服务器启动失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        print("感谢使用 Coze Studio 演示版!")

if __name__ == "__main__":
    main()
