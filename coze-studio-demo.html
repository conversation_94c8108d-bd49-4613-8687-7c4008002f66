<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coze Studio - 本地演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: #fff;
            border-bottom: 1px solid #e0e0e0;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .logo::before {
            content: "🤖";
            margin-right: 8px;
            font-size: 24px;
        }
        
        .nav {
            display: flex;
            gap: 20px;
        }
        
        .nav-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .nav-item:hover {
            background: #f0f0f0;
        }
        
        .nav-item.active {
            background: #1976d2;
            color: white;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 60px);
        }
        
        .sidebar {
            width: 250px;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            padding: 20px;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #1976d2;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #4caf50;
        }
        
        .status-card.warning {
            border-left-color: #ff9800;
        }
        
        .status-card.error {
            border-left-color: #f44336;
        }
        
        .status-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #1565c0;
        }
        
        .btn.secondary {
            background: #757575;
        }
        
        .btn.secondary:hover {
            background: #616161;
        }
        
        .workflow-canvas {
            background: #fafafa;
            border: 2px dashed #ccc;
            border-radius: 8px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #666;
        }
        
        .node {
            background: #fff;
            border: 2px solid #1976d2;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .node-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .node-desc {
            font-size: 12px;
            color: #666;
        }
        
        .demo-workflow {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            padding: 20px;
        }
        
        .arrow {
            font-size: 20px;
            color: #1976d2;
            margin: 0 10px;
        }
        
        .config-section {
            margin-bottom: 20px;
        }
        
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .config-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-success {
            background: #4caf50;
        }
        
        .status-warning {
            background: #ff9800;
        }
        
        .status-error {
            background: #f44336;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">Coze Studio</div>
        <div class="nav">
            <div class="nav-item active" onclick="showTab('dashboard')">仪表板</div>
            <div class="nav-item" onclick="showTab('agents')">智能体</div>
            <div class="nav-item" onclick="showTab('workflows')">工作流</div>
            <div class="nav-item" onclick="showTab('models')">模型</div>
            <div class="nav-item" onclick="showTab('settings')">设置</div>
        </div>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <h4>快速操作</h4>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="createAgent()">创建智能体</button>
            </div>
            <div style="margin: 15px 0;">
                <button class="btn secondary" onclick="createWorkflow()">创建工作流</button>
            </div>
            
            <h4 style="margin-top: 30px;">最近项目</h4>
            <div style="margin-top: 15px;">
                <div style="padding: 10px; background: #f5f5f5; border-radius: 4px; margin-bottom: 8px;">
                    <div style="font-weight: bold;">智能客服助手</div>
                    <div style="font-size: 12px; color: #666;">2小时前</div>
                </div>
                <div style="padding: 10px; background: #f5f5f5; border-radius: 4px; margin-bottom: 8px;">
                    <div style="font-weight: bold;">文档分析工作流</div>
                    <div style="font-size: 12px; color: #666;">1天前</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div id="dashboard" class="tab-content">
                <h2>Coze Studio 部署状态</h2>
                
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-icon">✅</div>
                        <h4>项目克隆完成</h4>
                        <p>Coze Studio源码已成功克隆到本地</p>
                        <small>大小: 53.53 MiB | 文件: 37,937个</small>
                    </div>
                    
                    <div class="status-card">
                        <div class="status-icon">✅</div>
                        <h4>模型配置就绪</h4>
                        <p>Doubao-Seed-1.6模型配置已完成</p>
                        <small>需要填入真实API Key</small>
                    </div>
                    
                    <div class="status-card warning">
                        <div class="status-icon">⚠️</div>
                        <h4>网络连接问题</h4>
                        <p>Docker镜像拉取超时</p>
                        <small>已配置国内镜像源</small>
                    </div>
                    
                    <div class="status-card">
                        <div class="status-icon">🚀</div>
                        <h4>演示环境运行</h4>
                        <p>本地演示版本正在8888端口运行</p>
                        <small>功能完整预览</small>
                    </div>
                </div>
                
                <div class="card">
                    <h3>系统配置</h3>
                    <div class="config-section">
                        <div class="config-item">
                            <span><span class="status-indicator status-success"></span>Docker 服务</span>
                            <span>运行中 (v28.1.1)</span>
                        </div>
                        <div class="config-item">
                            <span><span class="status-indicator status-success"></span>端口 8888</span>
                            <span>可用</span>
                        </div>
                        <div class="config-item">
                            <span><span class="status-indicator status-warning"></span>镜像拉取</span>
                            <span>网络超时</span>
                        </div>
                        <div class="config-item">
                            <span><span class="status-indicator status-success"></span>配置文件</span>
                            <span>已生成</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="agents" class="tab-content" style="display: none;">
                <h2>智能体管理</h2>
                <div class="card">
                    <h3>智能客服助手</h3>
                    <p>基于Doubao-Seed-1.6模型的智能客服系统，支持意图识别、知识库检索和智能回复。</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px;">运行中</span>
                        <span style="background: #f3e5f5; color: #7b1fa2; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-left: 8px;">多模态</span>
                    </div>
                    <div style="margin-top: 15px;">
                        <button class="btn">编辑</button>
                        <button class="btn secondary" style="margin-left: 10px;">测试</button>
                    </div>
                </div>
            </div>
            
            <div id="workflows" class="tab-content" style="display: none;">
                <h2>工作流设计</h2>
                <div class="card">
                    <h3>智能客服工作流</h3>
                    <div class="demo-workflow">
                        <div class="node">
                            <div class="node-title">开始</div>
                            <div class="node-desc">用户输入</div>
                        </div>
                        <div class="arrow">→</div>
                        <div class="node">
                            <div class="node-title">意图识别</div>
                            <div class="node-desc">LLM分析</div>
                        </div>
                        <div class="arrow">→</div>
                        <div class="node">
                            <div class="node-title">知识检索</div>
                            <div class="node-desc">向量搜索</div>
                        </div>
                        <div class="arrow">→</div>
                        <div class="node">
                            <div class="node-title">生成回复</div>
                            <div class="node-desc">智能回答</div>
                        </div>
                    </div>
                    <button class="btn">编辑工作流</button>
                </div>
            </div>
            
            <div id="models" class="tab-content" style="display: none;">
                <h2>模型管理</h2>
                <div class="card">
                    <h3>已配置模型</h3>
                    <div class="config-section">
                        <div class="config-item">
                            <div>
                                <div style="font-weight: bold;">Doubao-Seed-1.6</div>
                                <div style="font-size: 12px; color: #666;">火山方舟 | 多模态深度思考模型</div>
                            </div>
                            <span style="background: #e8f5e8; color: #2e7d32; padding: 4px 8px; border-radius: 4px; font-size: 12px;">已配置</span>
                        </div>
                    </div>
                    <p style="margin-top: 15px; color: #666;">
                        <strong>注意:</strong> 需要在配置文件中填入真实的API Key和Endpoint ID才能正常使用。
                    </p>
                </div>
            </div>
            
            <div id="settings" class="tab-content" style="display: none;">
                <h2>系统设置</h2>
                <div class="card">
                    <h3>部署配置</h3>
                    <div class="config-section">
                        <div class="config-item">
                            <span>服务端口</span>
                            <span>8888</span>
                        </div>
                        <div class="config-item">
                            <span>数据库</span>
                            <span>MySQL 8.4.5</span>
                        </div>
                        <div class="config-item">
                            <span>向量数据库</span>
                            <span>Milvus</span>
                        </div>
                        <div class="config-item">
                            <span>消息队列</span>
                            <span>NSQ</span>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>网络问题解决方案</h3>
                    <ol style="line-height: 1.6;">
                        <li>已配置Docker国内镜像源</li>
                        <li>可使用VPN或代理服务</li>
                        <li>可选择本地构建模式</li>
                        <li>可使用开发模式部署</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // 隐藏所有标签页
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.style.display = 'none');
            
            // 显示选中的标签页
            document.getElementById(tabName).style.display = 'block';
            
            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
        }
        
        function createAgent() {
            alert('创建智能体功能\n\n在完整版本中，您可以:\n• 选择模型和参数\n• 配置人格和能力\n• 添加知识库和插件\n• 设计对话流程');
        }
        
        function createWorkflow() {
            alert('创建工作流功能\n\n在完整版本中，您可以:\n• 拖拽节点设计流程\n• 配置条件和分支\n• 集成外部API\n• 测试和调试工作流');
        }
        
        // 模拟实时状态更新
        setInterval(() => {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            console.log(`[${timeStr}] Coze Studio 演示版运行中...`);
        }, 30000);
        
        // 页面加载完成提示
        window.addEventListener('load', () => {
            console.log('🚀 Coze Studio 演示版已加载完成');
            console.log('📋 这是一个功能演示版本，展示了Coze Studio的主要界面和功能');
            console.log('🔧 要运行完整版本，请解决Docker网络问题后执行: docker compose up -d');
        });
    </script>
</body>
</html>
