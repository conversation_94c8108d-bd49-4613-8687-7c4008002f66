{"name": "@coze-workflow/components", "version": "0.0.1", "description": "workflow 公共业务组件", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./workflow-modal": "./src/workflow-modal/index.tsx", "./use-workflow-resource-action": "./src/hooks/use-workflow-resource-action/index.tsx"}, "main": "src/index.ts", "typesVersions": {"*": {"workflow-modal": ["./src/workflow-modal/index.tsx"], "use-workflow-resource-action": ["./src/hooks/use-workflow-resource-action/index.tsx"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@codemirror/language": "^6.10.1", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.34.1", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/foundation-sdk": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/idl": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-common/assets": "workspace:*", "@coze-common/biz-components": "workspace:*", "@coze-common/loading-button": "workspace:*", "@coze-common/mouse-pad-selector": "workspace:*", "@coze-editor/editor": "0.1.0-alpha.dd871b", "@coze-studio/components": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/resources-adapter": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@flowgram-adapter/free-layout-editor": "workspace:*", "@tanstack/react-query": "~5.13.4", "ahooks": "^3.7.8", "classnames": "^2.3.2", "cronstrue": "2.54.0", "dayjs": "^1.11.7", "dequal": "^2.0.3", "eventemitter3": "^5.0.1", "immer": "^10.0.3", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "reflect-metadata": "^0.1.13", "semver": "^7.3.7", "slate": "0.101.5", "slate-history": "0.100.0", "slate-react": "0.101.5", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/tailwind-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@lezer/common": "^1.2.2", "@rspack/core": "0.6.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/semver": "^7.3.4", "@vitest/coverage-v8": "~3.0.5", "i18next": ">= 19.0.0", "less": "^3.13.1", "react": "~18.2.0", "react-dom": "~18.2.0", "react-is": ">= 16.8.0", "react-router-dom": "^6.22.0", "scheduler": ">=0.19.0", "styled-components": ">= 2", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "typescript": "~5.8.2", "utility-types": "^3.10.0", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5", "webpack": "~5.91.0"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}, "// deps": "immer@^10.0.3 为脚本自动补齐，请勿改动"}