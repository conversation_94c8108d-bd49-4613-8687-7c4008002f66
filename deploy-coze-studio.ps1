# Coze Studio 完整部署脚本
# 用于在网络问题解决后快速部署完整版本

param(
    [string]$ApiKey = "",
    [string]$EndpointId = "",
    [switch]$SkipNetworkCheck = $false,
    [switch]$UseLocalBuild = $false
)

Write-Host "🚀 Coze Studio 完整部署脚本" -ForegroundColor Green
Write-Host "=" * 50

# 检查参数
if (-not $ApiKey -or -not $EndpointId) {
    Write-Host "⚠️  警告: 未提供API Key或Endpoint ID" -ForegroundColor Yellow
    Write-Host "使用方法: .\deploy-coze-studio.ps1 -ApiKey 'your_api_key' -EndpointId 'your_endpoint_id'" -ForegroundColor Gray
    Write-Host "将使用占位符继续部署，稍后需要手动配置" -ForegroundColor Yellow
    Write-Host ""
}

# 检查Docker服务
Write-Host "📋 检查Docker服务..." -ForegroundColor Yellow
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker服务未运行，请启动Docker Desktop" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Docker服务正在运行" -ForegroundColor Green
} catch {
    Write-Host "❌ 无法连接到Docker服务" -ForegroundColor Red
    exit 1
}

# 网络连接测试
if (-not $SkipNetworkCheck) {
    Write-Host "🌐 测试网络连接..." -ForegroundColor Yellow
    try {
        $testResult = docker pull hello-world 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 网络连接正常" -ForegroundColor Green
            docker rmi hello-world 2>$null | Out-Null
        } else {
            Write-Host "⚠️  网络连接有问题，将使用本地构建模式" -ForegroundColor Yellow
            $UseLocalBuild = $true
        }
    } catch {
        Write-Host "⚠️  网络测试失败，将使用本地构建模式" -ForegroundColor Yellow
        $UseLocalBuild = $true
    }
}

# 进入项目目录
Write-Host "📁 进入项目目录..." -ForegroundColor Yellow
if (-not (Test-Path "coze-studio")) {
    Write-Host "❌ coze-studio目录不存在，请先克隆项目" -ForegroundColor Red
    exit 1
}

Set-Location "coze-studio"

# 配置模型API Key
if ($ApiKey -and $EndpointId) {
    Write-Host "🔧 配置模型API Key..." -ForegroundColor Yellow
    $modelConfigPath = "backend\conf\model\ark_doubao-seed-1.6.yaml"
    
    if (Test-Path $modelConfigPath) {
        $content = Get-Content $modelConfigPath -Raw
        $content = $content -replace 'api_key: "YOUR_VOLCENGINE_API_KEY_HERE"', "api_key: `"$ApiKey`""
        $content = $content -replace 'model: "YOUR_ENDPOINT_ID_HERE"', "model: `"$EndpointId`""
        $content | Set-Content $modelConfigPath -Encoding UTF8
        Write-Host "✅ 模型配置已更新" -ForegroundColor Green
    } else {
        Write-Host "⚠️  模型配置文件不存在: $modelConfigPath" -ForegroundColor Yellow
    }
}

# 进入docker目录
Set-Location "docker"

# 检查环境文件
Write-Host "📄 检查环境配置..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ 环境配置文件已创建" -ForegroundColor Green
    } else {
        Write-Host "❌ 环境配置模板不存在" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ 环境配置文件已存在" -ForegroundColor Green
}

# 配置构建模式
if ($UseLocalBuild) {
    Write-Host "🔨 配置本地构建模式..." -ForegroundColor Yellow
    $composeFile = "docker-compose.yml"
    $content = Get-Content $composeFile -Raw
    
    # 启用本地构建
    $content = $content -replace '#\s*build:', 'build:'
    $content = $content -replace '#\s*context: \.\./','  context: ../'
    $content = $content -replace '#\s*dockerfile: backend/Dockerfile','  dockerfile: backend/Dockerfile'
    $content = $content -replace 'image: opencoze/opencoze:latest','# image: opencoze/opencoze:latest'
    
    $content | Set-Content $composeFile -Encoding UTF8
    Write-Host "✅ 已配置为本地构建模式" -ForegroundColor Green
}

# 检查端口占用
Write-Host "🔍 检查端口占用..." -ForegroundColor Yellow
$portCheck = netstat -ano | Select-String ":8888"
if ($portCheck) {
    Write-Host "⚠️  端口8888被占用，正在尝试释放..." -ForegroundColor Yellow
    $processes = $portCheck | ForEach-Object { ($_ -split '\s+')[-1] } | Sort-Object -Unique
    foreach ($pid in $processes) {
        try {
            Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            Write-Host "✅ 已终止进程 PID: $pid" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  无法终止进程 PID: $pid" -ForegroundColor Yellow
        }
    }
    Start-Sleep -Seconds 2
}

# 启动服务
Write-Host "🚀 启动Coze Studio服务..." -ForegroundColor Yellow
Write-Host "这可能需要几分钟时间，请耐心等待..." -ForegroundColor Gray

try {
    if ($UseLocalBuild) {
        Write-Host "正在构建本地镜像..." -ForegroundColor Gray
        docker compose build --no-cache
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ 本地构建失败" -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host "正在启动服务容器..." -ForegroundColor Gray
    docker compose up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 服务启动成功!" -ForegroundColor Green
    } else {
        Write-Host "❌ 服务启动失败" -ForegroundColor Red
        Write-Host "查看日志: docker compose logs" -ForegroundColor Gray
        exit 1
    }
} catch {
    Write-Host "❌ 部署过程中出现错误: $_" -ForegroundColor Red
    exit 1
}

# 等待服务就绪
Write-Host "⏳ 等待服务就绪..." -ForegroundColor Yellow
$maxWait = 60
$waited = 0

do {
    Start-Sleep -Seconds 5
    $waited += 5
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8888" -TimeoutSec 5 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 服务已就绪!" -ForegroundColor Green
            break
        }
    } catch {
        # 继续等待
    }
    
    Write-Host "等待中... ($waited/$maxWait 秒)" -ForegroundColor Gray
} while ($waited -lt $maxWait)

if ($waited -ge $maxWait) {
    Write-Host "⚠️  服务启动超时，请检查日志" -ForegroundColor Yellow
    Write-Host "查看服务状态: docker compose ps" -ForegroundColor Gray
    Write-Host "查看日志: docker compose logs coze-server" -ForegroundColor Gray
} else {
    # 打开浏览器
    Write-Host "🌐 正在打开浏览器..." -ForegroundColor Green
    Start-Process "http://localhost:8888"
}

# 显示部署信息
Write-Host ""
Write-Host "🎉 Coze Studio 部署完成!" -ForegroundColor Green
Write-Host "=" * 50
Write-Host "📍 访问地址: http://localhost:8888" -ForegroundColor Cyan
Write-Host "📋 管理命令:" -ForegroundColor Yellow
Write-Host "   查看状态: docker compose ps" -ForegroundColor Gray
Write-Host "   查看日志: docker compose logs -f coze-server" -ForegroundColor Gray
Write-Host "   停止服务: docker compose down" -ForegroundColor Gray
Write-Host "   重启服务: docker compose restart" -ForegroundColor Gray

if (-not $ApiKey -or -not $EndpointId) {
    Write-Host ""
    Write-Host "⚠️  重要提醒:" -ForegroundColor Yellow
    Write-Host "请在以下文件中配置真实的API Key和Endpoint ID:" -ForegroundColor White
    Write-Host "backend\conf\model\ark_doubao-seed-1.6.yaml" -ForegroundColor Gray
    Write-Host "配置完成后重启服务: docker compose restart coze-server" -ForegroundColor Gray
}

Write-Host ""
Write-Host "📚 更多信息请查看 DEPLOYMENT_GUIDE.md" -ForegroundColor Blue
