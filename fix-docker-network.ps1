# Coze Studio Docker网络问题修复脚本
# 用于解决Docker镜像拉取超时问题

Write-Host "🔧 Coze Studio Docker网络问题修复脚本" -ForegroundColor Green
Write-Host "=" * 50

# 检查Docker是否运行
Write-Host "📋 检查Docker服务状态..." -ForegroundColor Yellow
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker服务正在运行" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker服务未运行，请启动Docker Desktop" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 无法连接到Docker服务" -ForegroundColor Red
    exit 1
}

# 创建Docker配置目录
$dockerConfigDir = "$env:USERPROFILE\.docker"
if (!(Test-Path $dockerConfigDir)) {
    Write-Host "📁 创建Docker配置目录: $dockerConfigDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $dockerConfigDir -Force | Out-Null
}

# 配置Docker镜像源
$daemonJsonPath = "$dockerConfigDir\daemon.json"
Write-Host "🔧 配置Docker镜像源..." -ForegroundColor Yellow

$daemonConfig = @{
    "registry-mirrors" = @(
        "https://docker.m.daocloud.io",
        "https://dockerproxy.com", 
        "https://docker.mirrors.ustc.edu.cn",
        "https://reg-mirror.qiniu.com"
    )
    "insecure-registries" = @()
    "debug" = $false
    "experimental" = $false
} | ConvertTo-Json -Depth 3

$daemonConfig | Out-File -FilePath $daemonJsonPath -Encoding UTF8
Write-Host "✅ Docker配置文件已创建: $daemonJsonPath" -ForegroundColor Green

# 显示配置内容
Write-Host "📄 配置内容:" -ForegroundColor Cyan
Get-Content $daemonJsonPath | Write-Host -ForegroundColor Gray

Write-Host ""
Write-Host "⚠️  重要提示:" -ForegroundColor Yellow
Write-Host "1. 请重启Docker Desktop以应用新配置" -ForegroundColor White
Write-Host "2. 重启后运行以下命令测试:" -ForegroundColor White
Write-Host "   docker pull hello-world" -ForegroundColor Gray
Write-Host "3. 如果仍有问题，请尝试使用VPN或代理" -ForegroundColor White

Write-Host ""
Write-Host "🚀 配置完成后，可以运行以下命令启动Coze Studio:" -ForegroundColor Green
Write-Host "cd coze-studio\docker" -ForegroundColor Gray
Write-Host "docker compose up -d" -ForegroundColor Gray

# 提供重启Docker的选项
Write-Host ""
$restart = Read-Host "是否现在重启Docker Desktop? (y/N)"
if ($restart -eq "y" -or $restart -eq "Y") {
    Write-Host "🔄 正在重启Docker Desktop..." -ForegroundColor Yellow
    
    # 停止Docker Desktop
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 5
    
    # 启动Docker Desktop
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop重启命令已执行" -ForegroundColor Green
    Write-Host "⏳ 请等待Docker完全启动后再继续..." -ForegroundColor Yellow
} else {
    Write-Host "ℹ️  请手动重启Docker Desktop以应用配置" -ForegroundColor Blue
}

Write-Host ""
Write-Host "🎉 脚本执行完成!" -ForegroundColor Green
