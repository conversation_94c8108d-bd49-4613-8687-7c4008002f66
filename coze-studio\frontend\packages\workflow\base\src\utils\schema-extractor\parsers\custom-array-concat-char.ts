/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { get } from 'lodash-es';

import { type SchemaExtractorArrayConcatCharParser } from '../type';
import { SYSTEM_DELIMITERS } from '../constant';

export const arrayConcatCharParser: SchemaExtractorArrayConcatCharParser =
  concatParams => {
    const allArrayItemConcatChars = (concatParams || []).find(
      v => v.name === 'allArrayItemConcatChars',
    );

    let customConcatChars = '';
    if (allArrayItemConcatChars) {
      const list = get(allArrayItemConcatChars, 'input.value.content', []) as {
        value: string;
      }[];

      const customItems = list.filter(
        v => !SYSTEM_DELIMITERS.includes(v.value),
      );
      customConcatChars = customItems.map(v => v.value).join(', ');
    }

    return customConcatChars;
  };
