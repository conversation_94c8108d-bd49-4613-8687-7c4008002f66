#!/usr/bin/env python3
import http.server
import socketserver
import webbrowser
from threading import Timer

PORT = 8888

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Coze Studio 测试页面</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .status { padding: 20px; border-radius: 5px; margin: 20px 0; }
                    .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
                    .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
                    .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🚀 Coze Studio 部署状态</h1>
                    
                    <div class="status success">
                        <h3>✅ 端口测试成功</h3>
                        <p>8888端口正常工作，可以接收HTTP请求</p>
                    </div>
                    
                    <div class="status warning">
                        <h3>⚠️ Docker镜像拉取问题</h3>
                        <p>由于网络连接问题，无法从Docker Hub拉取镜像。这是一个常见的网络问题。</p>
                        <h4>解决方案：</h4>
                        <ul>
                            <li>配置Docker镜像源为国内镜像</li>
                            <li>使用VPN或代理</li>
                            <li>使用本地构建</li>
                            <li>使用预构建的镜像</li>
                        </ul>
                    </div>
                    
                    <div class="status success">
                        <h3>✅ 项目克隆成功</h3>
                        <p>Coze Studio项目已成功克隆到本地</p>
                    </div>
                    
                    <div class="status success">
                        <h3>✅ 模型配置完成</h3>
                        <p>Doubao-Seed-1.6模型配置文件已创建（需要填入真实的API Key）</p>
                    </div>
                    
                    <h2>📋 下一步操作</h2>
                    <ol>
                        <li><strong>解决网络问题</strong>：配置Docker镜像源或使用代理</li>
                        <li><strong>配置API Key</strong>：在模型配置文件中填入真实的火山方舟API Key</li>
                        <li><strong>启动服务</strong>：运行 <code>docker compose up -d</code></li>
                        <li><strong>访问界面</strong>：打开 <a href="http://localhost:8888">http://localhost:8888</a></li>
                    </ol>
                    
                    <h2>🔧 配置文件位置</h2>
                    <ul>
                        <li>模型配置：<code>backend/conf/model/ark_doubao-seed-1.6.yaml</code></li>
                        <li>环境配置：<code>docker/.env</code></li>
                        <li>Docker配置：<code>docker/docker-compose.yml</code></li>
                    </ul>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html_content.encode())
        else:
            super().do_GET()

def open_browser():
    webbrowser.open('http://localhost:8888')

if __name__ == "__main__":
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"测试服务器启动在端口 {PORT}")
        print(f"访问 http://localhost:{PORT} 查看部署状态")
        
        # 2秒后自动打开浏览器
        Timer(2.0, open_browser).start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")
            httpd.shutdown()
