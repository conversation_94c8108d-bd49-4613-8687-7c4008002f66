# Coze Studio 部署指南

## 📋 项目概述

Coze Studio 是一站式 AI Agent 开发工具，提供可视化的智能体、应用和工作流开发环境。本指南记录了在Windows环境下的完整部署流程。

## ✅ 已完成的步骤

### 1. 环境准备 ✅
- **Docker**: 版本 28.1.1 ✅
- **Docker Compose**: 版本 v2.35.1-desktop.1 ✅
- **Docker服务**: 已启动并运行 ✅
- **端口检查**: 8888端口未被占用 ✅

### 2. 项目克隆 ✅
```bash
git clone https://github.com/coze-dev/coze-studio.git
```
- 项目大小: 53.53 MiB
- 文件数量: 37,937个文件
- 克隆状态: ✅ 成功

### 3. 模型配置 ✅
- 复制模板文件: `model_template_ark_doubao-seed-1.6.yaml` → `ark_doubao-seed-1.6.yaml`
- 配置位置: `backend/conf/model/ark_doubao-seed-1.6.yaml`
- 模型ID: 1 (已修改)
- API Key: 需要填入真实的火山方舟API Key
- Endpoint ID: 需要填入真实的Endpoint ID

### 4. 环境配置 ✅
- 复制环境文件: `.env.example` → `.env`
- 配置位置: `docker/.env`
- 服务端口: 8888 (已确认可用)

## ⚠️ 遇到的问题

### 网络连接问题
**问题描述**: Docker镜像拉取失败，出现TLS握手超时错误
```
error pulling image configuration: download failed after attempts=6: net/http: TLS handshake timeout
```

**影响范围**: 
- 无法拉取MySQL 8.4.5镜像
- 无法拉取Redis、Elasticsearch等中间件镜像
- 无法拉取Coze Studio主服务镜像

**根本原因**: 网络连接到Docker Hub不稳定

## 🔧 解决方案

### 方案1: 配置Docker镜像源 (推荐)
1. 创建或编辑Docker配置文件 `~/.docker/daemon.json`:
```json
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerproxy.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://reg-mirror.qiniu.com"
  ]
}
```

2. 重启Docker Desktop服务

3. 重新运行部署命令:
```bash
cd docker
docker compose up -d
```

### 方案2: 使用本地构建
1. 修改 `docker/docker-compose.yml`，启用本地构建:
```yaml
coze-server:
  build:
    context: ../
    dockerfile: backend/Dockerfile
  # image: opencoze/opencoze:latest
```

2. 构建并启动:
```bash
docker compose build
docker compose up -d
```

### 方案3: 使用开发模式
使用项目提供的Makefile进行本地开发:
```bash
# 安装依赖环境
make python
make middleware

# 构建前端
make fe

# 启动开发服务器
make server
```

## 📁 重要文件位置

### 配置文件
- **模型配置**: `backend/conf/model/ark_doubao-seed-1.6.yaml`
- **环境变量**: `docker/.env`
- **Docker配置**: `docker/docker-compose.yml`
- **开发环境**: `docker/.env.debug.example`

### 脚本文件
- **前端构建**: `scripts/build_fe.sh`
- **服务器构建**: `scripts/setup/server.sh`
- **数据库迁移**: `scripts/setup/db_migrate_apply.sh`

## 🚀 启动流程

### 生产环境部署
```bash
# 1. 进入项目目录
cd coze-studio/docker

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入必要的配置

# 3. 配置模型
# 编辑 ../backend/conf/model/ark_doubao-seed-1.6.yaml
# 填入真实的API Key和Endpoint ID

# 4. 启动服务
docker compose up -d

# 5. 访问服务
# 浏览器打开 http://localhost:8888
```

### 开发环境部署
```bash
# 1. 使用Makefile启动开发环境
make debug

# 2. 或者分步启动
make middleware  # 启动中间件
make fe         # 构建前端
make server     # 启动服务器
```

## 🔑 必需的配置

### 火山方舟API配置
在 `backend/conf/model/ark_doubao-seed-1.6.yaml` 中配置:
```yaml
id: 1  # 唯一模型ID
meta:
  conn_config:
    api_key: "YOUR_VOLCENGINE_API_KEY_HERE"  # 火山方舟API Key
    model: "YOUR_ENDPOINT_ID_HERE"           # Endpoint ID
```

### 获取API Key的方法
1. 访问 [火山方舟控制台](https://console.volcengine.com/ark)
2. 创建API Key
3. 获取Doubao-Seed-1.6模型的Endpoint ID

## 📊 服务验证

### 健康检查
```bash
# 检查容器状态
docker compose ps

# 检查服务日志
docker compose logs coze-server

# 检查端口占用
netstat -ano | findstr :8888
```

### 功能测试
1. 访问 http://localhost:8888
2. 创建新的智能体
3. 配置工作流
4. 测试对话功能

## 🎯 示例工作流

项目包含一个智能客服助手的示例工作流配置 (`example-workflow.json`)，展示了:
- 意图识别
- 知识库检索  
- 智能回复生成
- 条件判断
- 人工转接

## 🐛 常见问题

### 1. 端口被占用
```bash
# 查找占用8888端口的进程
netstat -ano | findstr :8888

# 杀死进程 (替换PID)
taskkill /PID <PID> /F
```

### 2. Docker服务未启动
```bash
# 启动Docker Desktop
Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
```

### 3. 权限问题
- 确保以管理员权限运行PowerShell
- 检查Docker Desktop的权限设置

## 📚 参考资源

- [Coze Studio官方文档](https://www.coze.cn/open/docs)
- [项目GitHub仓库](https://github.com/coze-dev/coze-studio)
- [火山方舟API文档](https://www.volcengine.com/docs/82379)

## 📝 下一步计划

1. 解决网络连接问题，完成Docker部署
2. 配置真实的API Key和模型参数
3. 创建和测试示例智能体
4. 集成更多模型和插件
5. 部署到生产环境

---

**部署状态**: 🟡 部分完成 (网络问题待解决)
**最后更新**: 2025-08-04
**维护者**: AI Assistant
