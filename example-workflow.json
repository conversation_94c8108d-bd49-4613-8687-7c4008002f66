{"workflow": {"name": "智能客服助手工作流", "description": "一个简单的智能客服助手工作流示例，展示如何使用Coze Studio创建AI Agent", "version": "1.0.0", "nodes": [{"id": "start", "type": "start", "name": "开始节点", "position": {"x": 100, "y": 100}, "config": {"input_variables": ["user_message", "user_context"]}}, {"id": "intent_recognition", "type": "llm", "name": "意图识别", "position": {"x": 300, "y": 100}, "config": {"model": "doubao-seed-1.6", "prompt": "你是一个智能客服助手的意图识别模块。请分析用户的消息，识别用户的意图。\n\n用户消息：{{user_message}}\n\n请从以下意图中选择最匹配的一个：\n1. 产品咨询\n2. 技术支持\n3. 订单查询\n4. 投诉建议\n5. 其他\n\n请只返回意图名称，不要包含其他内容。", "temperature": 0.3, "max_tokens": 50}, "inputs": {"user_message": "{{start.user_message}}"}, "outputs": {"intent": "{{intent_recognition.output}}"}}, {"id": "knowledge_search", "type": "knowledge_base", "name": "知识库检索", "position": {"x": 500, "y": 100}, "config": {"knowledge_base_id": "kb_001", "search_method": "semantic", "top_k": 3, "score_threshold": 0.7}, "inputs": {"query": "{{start.user_message}}"}, "outputs": {"relevant_docs": "{{knowledge_search.results}}"}}, {"id": "response_generation", "type": "llm", "name": "回复生成", "position": {"x": 700, "y": 100}, "config": {"model": "doubao-seed-1.6", "prompt": "你是一个专业的智能客服助手。请根据用户的意图和相关知识库内容，生成一个友好、专业、有帮助的回复。\n\n用户意图：{{intent_recognition.intent}}\n用户消息：{{start.user_message}}\n相关知识：{{knowledge_search.relevant_docs}}\n\n请生成一个简洁明了的回复：", "temperature": 0.7, "max_tokens": 500}, "inputs": {"intent": "{{intent_recognition.intent}}", "user_message": "{{start.user_message}}", "relevant_docs": "{{knowledge_search.relevant_docs}}"}, "outputs": {"response": "{{response_generation.output}}"}}, {"id": "satisfaction_check", "type": "condition", "name": "满意度检查", "position": {"x": 900, "y": 100}, "config": {"condition": "{{start.user_context.is_follow_up}} == false", "true_branch": "end", "false_branch": "escalation"}}, {"id": "escalation", "type": "action", "name": "人工转接", "position": {"x": 900, "y": 300}, "config": {"action_type": "human_handoff", "message": "正在为您转接人工客服，请稍候..."}}, {"id": "end", "type": "end", "name": "结束节点", "position": {"x": 1100, "y": 100}, "config": {"output_variables": {"final_response": "{{response_generation.response}}", "intent": "{{intent_recognition.intent}}", "confidence": "{{knowledge_search.max_score}}"}}}], "edges": [{"source": "start", "target": "intent_recognition"}, {"source": "intent_recognition", "target": "knowledge_search"}, {"source": "knowledge_search", "target": "response_generation"}, {"source": "response_generation", "target": "satisfaction_check"}, {"source": "satisfaction_check", "target": "end", "condition": "true"}, {"source": "satisfaction_check", "target": "escalation", "condition": "false"}]}, "agent_config": {"name": "智能客服助手", "description": "基于Coze Studio构建的智能客服助手，能够理解用户意图并提供专业回复", "avatar": "🤖", "personality": {"tone": "友好专业", "style": "简洁明了", "language": "中文"}, "capabilities": ["意图识别", "知识库检索", "智能回复", "人工转接"], "model_config": {"primary_model": "doubao-seed-1.6", "temperature": 0.7, "max_tokens": 1000}}, "knowledge_base": {"id": "kb_001", "name": "客服知识库", "description": "包含产品信息、常见问题、技术支持等内容", "documents": [{"title": "产品介绍", "content": "我们的产品是一个AI驱动的智能客服系统..."}, {"title": "常见问题FAQ", "content": "Q: 如何重置密码？\nA: 请点击登录页面的'忘记密码'链接..."}, {"title": "技术支持指南", "content": "遇到技术问题时，请先尝试以下步骤..."}]}}