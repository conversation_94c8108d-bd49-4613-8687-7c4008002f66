{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"jsx": "preserve", "strictNullChecks": true, "paths": {"@/*": ["./src/*"], "@shared/*": ["./shared/*"]}, "types": ["utility-types", "react", "react-dom", "vitest/globals"], "noImplicitReturns": false, "useUnknownInCatchVariables": false, "strictPropertyInitialization": false, "module": "ESNext", "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo", "moduleResolution": "bundler"}, "exclude": ["./src/**/*.test.ts"], "include": ["./src", "./src/**/*.json"], "references": [{"path": "../adapter/resources/tsconfig.build.json"}, {"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-error/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../arch/bot-store/tsconfig.build.json"}, {"path": "../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../arch/foundation-sdk/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/idl/tsconfig.build.json"}, {"path": "../../arch/logger/tsconfig.build.json"}, {"path": "../../arch/report-events/tsconfig.build.json"}, {"path": "../base/tsconfig.build.json"}, {"path": "../../common/assets/tsconfig.build.json"}, {"path": "../../common/biz-components/tsconfig.build.json"}, {"path": "../../common/flowgram-adapter/free-layout-editor/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../components/loading-button/tsconfig.build.json"}, {"path": "../../components/mouse-pad-selector/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/tailwind-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../studio/components/tsconfig.build.json"}]}